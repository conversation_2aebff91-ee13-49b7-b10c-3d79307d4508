<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Refresh Timing Test</title>
    
    <!-- Load existing project CSS -->
    <link rel="stylesheet" href="snapapp.css">
    
    <style>
        body {
            margin: 0;
            padding: 40px;
            background: var(--bg-secondary);
            color: var(--text-primary);
            font-family: 'Amazon Ember', Arial, sans-serif;
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            display: flex;
            flex-direction: column;
            gap: 24px;
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 24px;
        }
        
        .test-title {
            font-size: 28px;
            font-weight: 700;
            color: var(--text-accent);
            margin: 0 0 8px 0;
        }
        
        .demo-section {
            background: var(--bg-primary);
            border: 1.5px solid var(--border-color);
            border-radius: 14px;
            padding: 24px;
            text-align: center;
        }
        
        .refresh-demo {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
            margin: 20px 0;
        }
        
        .timing-info {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
            text-align: left;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
        }
        
        .status-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 8px 0;
            padding: 8px;
            border-radius: 4px;
            background: var(--bg-primary);
        }
        
        .status-label {
            font-weight: 600;
        }
        
        .status-value {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-idle { background-color: #4CAF50; }
        .status-active { background-color: #FF9800; }
        .status-disabled { background-color: #F44336; }
        
        .test-instructions {
            background: #E3F2FD;
            border: 1px solid #2196F3;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
            text-align: left;
        }
        
        [data-theme="dark"] .test-instructions {
            background: #0D1B2A;
            border-color: #1976D2;
        }
        
        .test-instructions h4 {
            margin: 0 0 12px 0;
            color: #1976D2;
            font-size: 16px;
        }
        
        [data-theme="dark"] .test-instructions h4 {
            color: #64B5F6;
        }
        
        .test-instructions ol {
            margin: 0;
            padding-left: 20px;
        }
        
        .test-instructions li {
            margin: 6px 0;
            font-size: 14px;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <!-- Header -->
        <div class="test-header">
            <h1 class="test-title">Dashboard Refresh Timing Test</h1>
            <p>Verify that the refresh button is properly disabled during refresh and re-enabled when complete</p>
        </div>
        
        <!-- Demo Section -->
        <div class="demo-section">
            <h3>🔄 Refresh Button Behavior Test</h3>
            
            <div class="refresh-demo">
                <!-- Dashboard Refresh Button Mount -->
                <div id="dashboard-refresh-mount"></div>
            </div>
            
            <div class="timing-info">
                <div class="status-row">
                    <span class="status-label">Refresh State:</span>
                    <span class="status-value" id="refreshState">
                        <span class="status-indicator status-idle"></span>Idle
                    </span>
                </div>
                <div class="status-row">
                    <span class="status-label">Button State:</span>
                    <span class="status-value" id="buttonState">
                        <span class="status-indicator status-idle"></span>Enabled
                    </span>
                </div>
                <div class="status-row">
                    <span class="status-label">Refresh Duration:</span>
                    <span class="status-value" id="refreshDuration">0ms</span>
                </div>
                <div class="status-row">
                    <span class="status-label">Last Refresh:</span>
                    <span class="status-value" id="lastRefresh">Never</span>
                </div>
            </div>
            
            <div class="test-instructions">
                <h4>🧪 Testing Instructions:</h4>
                <ol>
                    <li><strong>Single Click:</strong> Click the refresh button once and observe the states</li>
                    <li><strong>Expected Behavior:</strong> 
                        <ul>
                            <li>Button immediately becomes disabled with spinning animation</li>
                            <li>Refresh state shows "Active"</li>
                            <li>After ~3-5 seconds, button re-enables and refresh state returns to "Idle"</li>
                        </ul>
                    </li>
                    <li><strong>Multiple Clicks:</strong> Try clicking multiple times while refresh is active</li>
                    <li><strong>Expected Behavior:</strong> Additional clicks should be ignored (button is disabled)</li>
                    <li><strong>Console Logs:</strong> Open DevTools to see detailed refresh progress</li>
                </ol>
            </div>
        </div>
        
        <!-- Fix Summary -->
        <div class="demo-section">
            <h3>✅ Fix Summary</h3>
            <div style="text-align: left;">
                <p><strong>Problem Fixed:</strong> Refresh button could be clicked multiple times, causing infinite loops</p>
                <p><strong>Solution:</strong></p>
                <ul style="margin: 12px 0; padding-left: 20px;">
                    <li>Button is immediately disabled when refresh starts</li>
                    <li>Global state prevents overlapping refresh operations</li>
                    <li>Button is re-enabled only after ALL card refresh operations complete</li>
                    <li>Proper error handling ensures button doesn't get stuck</li>
                </ul>
                <p><strong>Key Change:</strong> State reset moved from <code>finally</code> block to after <code>await</code> completion</p>
            </div>
        </div>
    </div>

    <!-- Load Dashboard JavaScript -->
    <script src="components/dashboard/dashboard.js"></script>
    
    <script>
        let refreshStateElement = document.getElementById('refreshState');
        let buttonStateElement = document.getElementById('buttonState');
        let refreshDurationElement = document.getElementById('refreshDuration');
        let lastRefreshElement = document.getElementById('lastRefresh');
        
        let refreshStartTime = null;
        let refreshEndTime = null;
        
        // Initialize dashboard refresh button
        function initializeDashboard() {
            // Set up the dashboard refresh functionality
            if (typeof setupDashboardRefresh === 'function') {
                setupDashboardRefresh();
                console.log('✅ Dashboard refresh functionality initialized');
            } else {
                console.error('❌ setupDashboardRefresh function not found');
            }
            
            // Monitor refresh state changes
            monitorRefreshState();
        }
        
        // Monitor refresh state and update UI
        function monitorRefreshState() {
            setInterval(() => {
                const isRefreshing = window.isDashboardRefreshing || false;
                const refreshBtn = document.querySelector('.dashboard-refresh-btn');
                
                // Update refresh state
                if (isRefreshing) {
                    if (!refreshStartTime) {
                        refreshStartTime = Date.now();
                    }
                    refreshStateElement.innerHTML = '<span class="status-indicator status-active"></span>Active (Refreshing...)';
                } else {
                    if (refreshStartTime && !refreshEndTime) {
                        refreshEndTime = Date.now();
                        const duration = refreshEndTime - refreshStartTime;
                        refreshDurationElement.textContent = `${duration}ms`;
                        lastRefreshElement.textContent = new Date().toLocaleTimeString();
                        
                        // Reset for next refresh
                        setTimeout(() => {
                            refreshStartTime = null;
                            refreshEndTime = null;
                        }, 1000);
                    }
                    refreshStateElement.innerHTML = '<span class="status-indicator status-idle"></span>Idle';
                }
                
                // Update button state
                if (refreshBtn) {
                    const isDisabled = refreshBtn.disabled;
                    const hasSpinning = refreshBtn.classList.contains('refreshing');
                    
                    if (isDisabled || hasSpinning) {
                        buttonStateElement.innerHTML = '<span class="status-indicator status-disabled"></span>Disabled (Refreshing)';
                    } else {
                        buttonStateElement.innerHTML = '<span class="status-indicator status-idle"></span>Enabled';
                    }
                }
                
                // Update duration during refresh
                if (isRefreshing && refreshStartTime) {
                    const currentDuration = Date.now() - refreshStartTime;
                    refreshDurationElement.textContent = `${currentDuration}ms (ongoing)`;
                }
                
            }, 100);
        }
        
        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            // Set light theme
            document.documentElement.setAttribute('data-theme', 'light');
            
            // Initialize dashboard
            setTimeout(initializeDashboard, 500);
        });
    </script>
</body>
</html>
